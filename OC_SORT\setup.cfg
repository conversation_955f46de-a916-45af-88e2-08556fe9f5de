[isort]
line_length = 100
multi_line_output = 3
balanced_wrapping = True
known_standard_library = setuptools
known_third_party = tqdm,loguru
known_data_processing = cv2,numpy,scipy,PIL,matplotlib,scikit_image
known_datasets = pycocotools
known_deeplearning = torch,torchvision,caffe2,onnx,apex,timm,thop,torch2trt,tensorrt,openvino,onnxruntime
known_myself = yolox
sections = FUTURE,STDLIB,THIRDPARTY,data_processing,datasets,deeplearning,myself,FIRSTPARTY,LOCALFOLDER
no_lines_before=STDLIB,THIRDPARTY,datasets
default_section = FIRSTPARTY

[flake8]
max-line-length = 100
max-complexity = 18
exclude = __init__.py
