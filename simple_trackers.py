"""
Simplified implementations of ByteTrack and OC-SORT for deployment
These are standalone implementations that don't require external dependencies
"""
import numpy as np
import cv2
from scipy.optimize import linear_sum_assignment
from scipy.spatial.distance import cdist

class KalmanFilter:
    """Simple Kalman filter for tracking"""
    def __init__(self):
        # State: [x, y, vx, vy, w, h]
        self.x = np.zeros(6)
        self.P = np.eye(6) * 1000  # Covariance matrix
        self.F = np.array([  # State transition matrix
            [1, 0, 1, 0, 0, 0],
            [0, 1, 0, 1, 0, 0],
            [0, 0, 1, 0, 0, 0],
            [0, 0, 0, 1, 0, 0],
            [0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 1]
        ])
        self.H = np.array([  # Observation matrix
            [1, 0, 0, 0, 0, 0],
            [0, 1, 0, 0, 0, 0],
            [0, 0, 0, 0, 1, 0],
            [0, 0, 0, 0, 0, 1]
        ])
        self.Q = np.eye(6) * 0.1  # Process noise
        self.R = np.eye(4) * 1    # Measurement noise
        
    def predict(self):
        """Predict next state"""
        self.x = self.F @ self.x
        self.P = self.F @ self.P @ self.F.T + self.Q
        return self.x[:4]  # Return [x, y, w, h]
        
    def update(self, measurement):
        """Update with measurement [x, y, w, h]"""
        z = np.array(measurement)
        y = z - self.H @ self.x  # Innovation
        S = self.H @ self.P @ self.H.T + self.R  # Innovation covariance
        K = self.P @ self.H.T @ np.linalg.inv(S)  # Kalman gain
        
        self.x = self.x + K @ y
        self.P = (np.eye(6) - K @ self.H) @ self.P
        
    def get_state(self):
        """Get current state [x, y, w, h]"""
        return self.x[:4]

class SimpleTrack:
    """Simple track for ByteTrack-like tracking"""
    count = 0
    
    def __init__(self, detection, track_id=None):
        self.track_id = track_id if track_id is not None else SimpleTrack.count
        SimpleTrack.count += 1
        
        # Detection format: [x1, y1, x2, y2, conf]
        x1, y1, x2, y2, conf = detection
        self.bbox = [x1, y1, x2 - x1, y2 - y1]  # [x, y, w, h]
        self.score = conf
        
        # Initialize Kalman filter
        self.kalman = KalmanFilter()
        self.kalman.x[:4] = self.bbox
        
        # Track state
        self.time_since_update = 0
        self.hit_streak = 1
        self.age = 1
        self.state = 'tentative'  # tentative, confirmed, deleted
        
    def predict(self):
        """Predict next position"""
        predicted = self.kalman.predict()
        self.bbox = predicted
        self.age += 1
        self.time_since_update += 1
        
    def update(self, detection):
        """Update track with new detection"""
        x1, y1, x2, y2, conf = detection
        measurement = [x1, y1, x2 - x1, y2 - y1]
        
        self.kalman.update(measurement)
        self.bbox = self.kalman.get_state()
        self.score = conf
        
        self.time_since_update = 0
        self.hit_streak += 1
        
        if self.state == 'tentative' and self.hit_streak >= 3:
            self.state = 'confirmed'
            
    def get_bbox(self):
        """Get bounding box in [x1, y1, x2, y2] format"""
        x, y, w, h = self.bbox
        return [x, y, x + w, y + h]

class SimpleBYTETracker:
    """Simplified ByteTrack implementation"""
    def __init__(self, track_thresh=0.5, track_buffer=30, match_thresh=0.8):
        self.track_thresh = track_thresh
        self.track_buffer = track_buffer
        self.match_thresh = match_thresh
        
        self.tracked_tracks = []
        self.lost_tracks = []
        self.removed_tracks = []
        
        self.frame_id = 0
        
    def update(self, detections, img_info=None, img_size=None):
        """Update tracks with new detections"""
        self.frame_id += 1
        
        # Separate high and low confidence detections
        if len(detections) > 0:
            high_conf = detections[detections[:, 4] >= self.track_thresh]
            low_conf = detections[detections[:, 4] < self.track_thresh]
        else:
            high_conf = np.empty((0, 5))
            low_conf = np.empty((0, 5))
            
        # Predict all tracks
        for track in self.tracked_tracks:
            track.predict()
            
        # First association with high confidence detections
        matched, unmatched_dets, unmatched_trks = self._associate(
            high_conf, self.tracked_tracks, self.match_thresh)
            
        # Update matched tracks
        for m in matched:
            self.tracked_tracks[m[1]].update(high_conf[m[0]])
            
        # Second association with low confidence detections
        if len(low_conf) > 0 and len(unmatched_trks) > 0:
            unmatched_tracks = [self.tracked_tracks[i] for i in unmatched_trks]
            matched_low, unmatched_dets_low, unmatched_trks_low = self._associate(
                low_conf, unmatched_tracks, 0.5)
                
            for m in matched_low:
                unmatched_tracks[m[1]].update(low_conf[m[0]])
                
            unmatched_trks = [unmatched_trks[i] for i in unmatched_trks_low]
            
        # Create new tracks for unmatched high confidence detections
        for i in unmatched_dets:
            track = SimpleTrack(high_conf[i])
            self.tracked_tracks.append(track)
            
        # Remove tracks that haven't been updated
        self.tracked_tracks = [t for t in self.tracked_tracks 
                             if t.time_since_update <= self.track_buffer]
        
        # Return active tracks
        active_tracks = []
        for track in self.tracked_tracks:
            if track.state == 'confirmed' or track.hit_streak >= 3:
                bbox = track.get_bbox()
                active_tracks.append([bbox[0], bbox[1], bbox[2], bbox[3], 
                                    track.track_id, track.score])
                
        return np.array(active_tracks) if active_tracks else np.empty((0, 6))
        
    def _associate(self, detections, tracks, thresh):
        """Associate detections with tracks using IoU"""
        if len(tracks) == 0:
            return [], list(range(len(detections))), []
            
        # Calculate IoU matrix
        iou_matrix = np.zeros((len(detections), len(tracks)))
        for d, det in enumerate(detections):
            for t, track in enumerate(tracks):
                iou_matrix[d, t] = self._calculate_iou(det[:4], track.get_bbox())
                
        # Hungarian algorithm for assignment
        if iou_matrix.size > 0:
            matched_indices = linear_sum_assignment(-iou_matrix)
            matched_indices = np.array(list(zip(matched_indices[0], matched_indices[1])))
        else:
            matched_indices = np.empty((0, 2), dtype=int)
            
        # Filter matches by threshold
        matches = []
        for m in matched_indices:
            if iou_matrix[m[0], m[1]] >= thresh:
                matches.append(m)
                
        unmatched_dets = [d for d in range(len(detections)) 
                         if d not in [m[0] for m in matches]]
        unmatched_trks = [t for t in range(len(tracks)) 
                         if t not in [m[1] for m in matches]]
                         
        return matches, unmatched_dets, unmatched_trks
        
    def _calculate_iou(self, bbox1, bbox2):
        """Calculate IoU between two bounding boxes"""
        # Convert to [x1, y1, x2, y2] format
        if len(bbox1) == 4 and len(bbox2) == 4:
            x1_1, y1_1, x2_1, y2_1 = bbox1
            x1_2, y1_2, x2_2, y2_2 = bbox2
        else:
            return 0
            
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0

class SimpleOCSORT:
    """Simplified OC-SORT implementation"""
    def __init__(self, det_thresh=0.5, max_age=30, min_hits=3, iou_threshold=0.3):
        self.det_thresh = det_thresh
        self.max_age = max_age
        self.min_hits = min_hits
        self.iou_threshold = iou_threshold
        
        self.tracks = []
        self.frame_count = 0
        
    def update(self, detections, img_info=None, img_size=None):
        """Update tracks with new detections"""
        self.frame_count += 1
        
        # Filter detections by confidence
        if len(detections) > 0:
            valid_dets = detections[detections[:, 4] >= self.det_thresh]
        else:
            valid_dets = np.empty((0, 5))
            
        # Predict all tracks
        for track in self.tracks:
            track.predict()
            
        # Associate detections with tracks
        if len(valid_dets) > 0 and len(self.tracks) > 0:
            matched, unmatched_dets, unmatched_trks = self._associate(
                valid_dets, self.tracks, self.iou_threshold)
                
            # Update matched tracks
            for m in matched:
                self.tracks[m[1]].update(valid_dets[m[0]])
                
            # Create new tracks for unmatched detections
            for i in unmatched_dets:
                track = SimpleTrack(valid_dets[i])
                self.tracks.append(track)
                
        elif len(valid_dets) > 0:
            # Create new tracks for all detections
            for det in valid_dets:
                track = SimpleTrack(det)
                self.tracks.append(track)
                
        # Remove old tracks
        self.tracks = [t for t in self.tracks if t.time_since_update <= self.max_age]
        
        # Return confirmed tracks
        active_tracks = []
        for track in self.tracks:
            if track.hit_streak >= self.min_hits and track.time_since_update <= 1:
                bbox = track.get_bbox()
                active_tracks.append([bbox[0], bbox[1], bbox[2], bbox[3], 
                                    track.track_id, track.score])
                
        return np.array(active_tracks) if active_tracks else np.empty((0, 6))
        
    def _associate(self, detections, tracks, thresh):
        """Associate detections with tracks using IoU"""
        if len(tracks) == 0:
            return [], list(range(len(detections))), []
            
        # Calculate IoU matrix
        iou_matrix = np.zeros((len(detections), len(tracks)))
        for d, det in enumerate(detections):
            for t, track in enumerate(tracks):
                iou_matrix[d, t] = self._calculate_iou(det[:4], track.get_bbox())
                
        # Hungarian algorithm for assignment
        if iou_matrix.size > 0:
            matched_indices = linear_sum_assignment(-iou_matrix)
            matched_indices = np.array(list(zip(matched_indices[0], matched_indices[1])))
        else:
            matched_indices = np.empty((0, 2), dtype=int)
            
        # Filter matches by threshold
        matches = []
        for m in matched_indices:
            if iou_matrix[m[0], m[1]] >= thresh:
                matches.append(m)
                
        unmatched_dets = [d for d in range(len(detections)) 
                         if d not in [m[0] for m in matches]]
        unmatched_trks = [t for t in range(len(tracks)) 
                         if t not in [m[1] for m in matches]]
                         
        return matches, unmatched_dets, unmatched_trks
        
    def _calculate_iou(self, bbox1, bbox2):
        """Calculate IoU between two bounding boxes"""
        # Convert to [x1, y1, x2, y2] format
        if len(bbox1) == 4 and len(bbox2) == 4:
            x1_1, y1_1, x2_1, y2_1 = bbox1
            x1_2, y1_2, x2_2, y2_2 = bbox2
        else:
            return 0
            
        # Calculate intersection
        x1_i = max(x1_1, x1_2)
        y1_i = max(y1_1, y1_2)
        x2_i = min(x2_1, x2_2)
        y2_i = min(y2_1, y2_2)
        
        if x2_i <= x1_i or y2_i <= y1_i:
            return 0
            
        intersection = (x2_i - x1_i) * (y2_i - y1_i)
        area1 = (x2_1 - x1_1) * (y2_1 - y1_1)
        area2 = (x2_2 - x1_2) * (y2_2 - y1_2)
        union = area1 + area2 - intersection
        
        return intersection / union if union > 0 else 0
