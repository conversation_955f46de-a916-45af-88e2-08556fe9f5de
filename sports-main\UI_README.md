# ⚽ Sports Analytics UI - Soccer Video Analysis

A comprehensive user interface for analyzing soccer videos using advanced AI detection models and the sports library.

## 🚀 **New Features - Real Sports Detection Integration**

### **Real AI Detection Models**
- **Player Detection**: Uses YOLO models with sports library annotation
- **Ball Detection**: Integrates BallTracker and BallAnnotator from sports library
- **Pitch Detection**: Uses soccer pitch configuration and drawing functions
- **Player Tracking**: Real-time tracking with ByteTrack and sports annotators
- **Team Classification**: AI-powered team identification using TeamClassifier
- **Radar View**: Dynamic radar with real player positions

### **Confidence Control**
- **Live Confidence Slider**: Adjust detection sensitivity in real-time (0.1 - 0.9)
- **Dynamic Threshold**: Change confidence without stopping analysis
- **Performance Optimization**: Lower confidence for faster processing, higher for accuracy

## 🎯 **What You'll See Now**

### **Live Analysis Tab Features:**
✅ **Real AI Detection** - No more simulated boxes!  
✅ **Confidence Slider** - Control detection sensitivity live  
✅ **Sports Library Integration** - Uses actual detection algorithms  
✅ **Real-time Performance** - Live FPS and frame monitoring  
✅ **Professional Annotations** - Industry-standard detection visualization  

### **Each Analysis Mode Now Shows:**
- **Player Detection**: Real player boxes with AI confidence scores
- **Ball Detection**: Ball tracking with trajectory visualization
- **Pitch Detection**: Professional soccer field markings
- **Player Tracking**: Unique player IDs with movement tracking
- **Team Classification**: AI-powered team color identification
- **Radar View**: Live player position mapping

## 🔧 **Installation & Setup**

### **1. Install Dependencies**
```bash
pip install -r ui_requirements.txt
```

### **2. Launch the UI**
```bash
# Windows
run_sports_ui.bat

# Linux/macOS
./run_sports_ui.sh

# Or directly with Python
python launch_ui.py
```

## 📱 **How to Use**

### **Configuration Tab:**
1. **Select Video**: Choose your soccer video file
2. **Choose Mode**: Select analysis type
3. **Set Device**: CPU or GPU (CUDA)
4. **Configure Output**: Set output directory

### **Live Analysis Tab:**
1. **Adjust Confidence**: Use the slider to control detection sensitivity
2. **Start Analysis**: Click "🎬 Start Live Analysis"
3. **Watch Real-time**: See AI detection happening live
4. **Change Modes**: Switch between detection types on the fly
5. **Monitor Performance**: Real-time FPS and frame count

### **Confidence Slider Usage:**
- **0.1 - 0.3**: Fast detection, may miss some objects
- **0.4 - 0.6**: Balanced speed and accuracy (recommended)
- **0.7 - 0.9**: High accuracy, slower processing

## 🎬 **Analysis Modes**

### **1. Player Detection**
- Detects players, goalkeepers, and referees
- Real-time bounding boxes with labels
- Confidence-based filtering

### **2. Ball Detection**
- Tracks ball movement across frames
- Uses sports library BallTracker
- Trajectory visualization with color coding

### **3. Pitch Detection**
- Identifies soccer field keypoints
- Draws professional field markings
- Uses SoccerPitchConfiguration

### **4. Player Tracking**
- Assigns unique IDs to players
- Tracks movement across frames
- Real-time ID persistence

### **5. Team Classification**
- AI-powered team identification
- Uses SiglipVisionModel for feature extraction
- Color-coded team visualization

### **6. Radar View**
- Top-down field perspective
- Real player positions mapped
- Dynamic position updates

## 🔍 **Technical Details**

### **Sports Library Integration:**
- **BallAnnotator**: Professional ball visualization
- **BallTracker**: Intelligent ball position prediction
- **TeamClassifier**: Deep learning team identification
- **SoccerPitchConfiguration**: Professional field specifications
- **ByteTrack**: State-of-the-art player tracking

### **Performance Features:**
- **Real-time Processing**: 30 FPS live analysis
- **Confidence Control**: Dynamic threshold adjustment
- **Fallback Systems**: Graceful degradation if models fail
- **Error Handling**: Comprehensive logging and error recovery

## 📊 **Output Files**

The UI generates:
- **Processed Videos**: Annotated with detection results
- **Analysis Logs**: Detailed processing information
- **Performance Metrics**: FPS, detection counts, timing data

## 🛠️ **Troubleshooting**

### **Common Issues:**
1. **Models Not Loading**: Check if YOLO models are available
2. **Sports Library Errors**: Verify all dependencies are installed
3. **Performance Issues**: Lower confidence threshold for better speed
4. **Detection Quality**: Increase confidence for better accuracy

### **Error Messages:**
- **"Sports modules not available"**: Install missing dependencies
- **"Model initialization error"**: Check model file paths
- **"Detection error"**: Lower confidence or check video format

## 🎨 **Customization**

### **Visual Settings:**
- **Colors**: Modify detection box colors
- **Thickness**: Adjust annotation line thickness
- **Labels**: Customize detection labels
- **Overlays**: Modify pitch and radar visualizations

### **Performance Tuning:**
- **Confidence Thresholds**: Set default confidence values
- **Frame Processing**: Adjust processing resolution
- **Tracking Parameters**: Modify tracking sensitivity

## 🚀 **Advanced Usage**

### **Batch Processing:**
- Process multiple videos sequentially
- Batch confidence settings
- Automated output organization

### **Model Switching:**
- Switch between different YOLO models
- Custom model integration
- Model performance comparison

### **Export Options:**
- Export detection data as JSON
- Save annotated frames as images
- Generate analysis reports

---

**🎯 The UI now provides professional-grade soccer video analysis with real AI detection, live confidence control, and full sports library integration!**
