#!/usr/bin/env python3
"""
Sports Analytics UI Launcher
This script checks dependencies and launches the Sports Analytics UI application.
"""

import sys
import subprocess
import importlib.util

def check_dependency(package_name, import_name=None):
    """Check if a package is available"""
    if import_name is None:
        import_name = package_name
        
    try:
        importlib.util.find_spec(import_name)
        return True
    except ImportError:
        return False

def install_dependency(package_name):
    """Install a missing dependency"""
    print(f"Installing {package_name}...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_name])
        return True
    except subprocess.CalledProcessError:
        print(f"Failed to install {package_name}")
        return False

def main():
    print("⚽ Sports Analytics UI - Dependency Check")
    print("=" * 50)
    
    # Required packages
    required_packages = [
        ("ultralytics", "ultralytics"),
        ("supervision", "supervision"),
        ("opencv-python", "cv2"),
        ("numpy", "numpy"),
        ("Pillow", "PIL"),
    ]
    
    missing_packages = []
    
    # Check each package
    for package_name, import_name in required_packages:
        if check_dependency(package_name, import_name):
            print(f"✅ {package_name} - OK")
        else:
            print(f"❌ {package_name} - Missing")
            missing_packages.append(package_name)
    
    # Install missing packages if any
    if missing_packages:
        print(f"\nInstalling {len(missing_packages)} missing package(s)...")
        for package in missing_packages:
            if not install_dependency(package):
                print(f"\n❌ Failed to install {package}. Please install manually:")
                print(f"pip install {package}")
                return False
        print("\n✅ All packages installed successfully!")
    
    # Launch the UI
    print("\n🚀 Launching Sports Analytics UI...")
    try:
        from sports_ui import main as launch_ui
        launch_ui()
    except ImportError as e:
        print(f"❌ Error importing sports_ui: {e}")
        print("Make sure you're running this script from the sports-main directory.")
        return False
    except Exception as e:
        print(f"❌ Error launching UI: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        print("\n❌ Failed to launch Sports Analytics UI")
        print("Please check the error messages above and try again.")
        input("Press Enter to exit...")
        sys.exit(1)
