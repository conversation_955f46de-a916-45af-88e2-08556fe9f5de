import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import os
import sys
import subprocess
from pathlib import Path
import json
from datetime import datetime
import cv2
from PIL import Image, ImageTk
import numpy as np

# Sports detection imports
try:
    from ultralytics import YOLO
    import supervision as sv
    from sports.annotators.soccer import draw_pitch, draw_points_on_pitch
    from sports.common.ball import BallTracker, BallAnnotator
    from sports.common.team import TeamClassifier
    from sports.common.view import ViewTransformer
    from sports.configs.soccer import SoccerPitchConfiguration
    SPORTS_AVAILABLE = True
except ImportError as e:
    print(f"Sports modules not available: {e}")
    SPORTS_AVAILABLE = False

class SportsAnalyticsUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Sports Analytics - Soccer Video Analysis")
        self.root.geometry("900x700")
        self.root.configure(bg='#f0f0f0')
        
        # Variables
        self.source_video_path = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.device_var = tk.StringVar(value="cpu")
        self.mode_var = tk.StringVar(value="PLAYER_DETECTION")
        self.is_processing = False
        
        # Live analysis variables
        self.is_live_analysis = False
        self.cap = None
        self.current_frame = None
        self.frame_count = 0
        self.fps_counter = 0
        self.last_fps_time = datetime.now()
        
        # Sports detection models
        self.player_model = None
        self.ball_model = None
        self.pitch_model = None
        self.team_classifier = None
        self.ball_tracker = None
        self.ball_annotator = None
        self.tracker = None
        self.confidence_threshold = 0.5
        
        # Initialize models if available
        if SPORTS_AVAILABLE:
            self.initialize_models()
        
        # Analysis modes
        self.modes = {
            "PITCH_DETECTION": "Detect and analyze soccer pitch keypoints",
            "PLAYER_DETECTION": "Detect players, goalkeepers, and referees",
            "BALL_DETECTION": "Track ball movement throughout the video",
            "PLAYER_TRACKING": "Track individual players across frames",
            "TEAM_CLASSIFICATION": "Classify players by team colors",
            "RADAR": "Generate radar view with player positions"
        }
        
        self.setup_ui()
        
    def setup_ui(self):
        # Main title
        title_frame = tk.Frame(self.root, bg='#2c3e50', height=80)
        title_frame.pack(fill='x', padx=10, pady=10)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(
            title_frame, 
            text="⚽ Sports Analytics - Soccer Video Analysis", 
            font=('Arial', 20, 'bold'), 
            fg='white', 
            bg='#2c3e50'
        )
        title_label.pack(expand=True)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill='both', expand=True, padx=20, pady=(0, 10))
        
        # Configuration tab
        self.config_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.config_tab, text="⚙️ Configuration")
        
        # Live analysis tab
        self.live_tab = ttk.Frame(self.notebook)
        self.notebook.add(self.live_tab, text="📹 Live Analysis")
        
        # Setup configuration tab content
        self.setup_config_tab()
        
        # Setup live analysis tab content
        self.setup_live_tab()
        
        # Bind window resize event
        self.root.bind('<Configure>', self.on_window_resize)
        
    def initialize_models(self):
        """Initialize sports detection models"""
        try:
            # Get the path to the sports examples directory
            sports_dir = os.path.join(os.path.dirname(__file__), 'examples', 'soccer')
            data_dir = os.path.join(sports_dir, 'data')
            
            # Check if models exist, if not, use default YOLO models
            player_model_path = os.path.join(data_dir, 'football-player-detection.pt')
            ball_model_path = os.path.join(data_dir, 'football-ball-detection.pt')
            pitch_model_path = os.path.join(data_dir, 'football-pitch-detection.pt')
            
            # Initialize player detection model
            if os.path.exists(player_model_path):
                self.player_model = YOLO(player_model_path)
                self.log_message("Player detection model loaded successfully")
            else:
                # Use default YOLO model for player detection
                self.player_model = YOLO('yolov8n.pt')
                self.log_message("Using default YOLO model for player detection")
            
            # Initialize ball detection model
            if os.path.exists(ball_model_path):
                self.ball_model = YOLO(ball_model_path)
                self.log_message("Ball detection model loaded successfully")
            else:
                # Use default YOLO model for ball detection
                self.ball_model = YOLO('yolov8n.pt')
                self.log_message("Using default YOLO model for ball detection")
            
            # Initialize pitch detection model
            if os.path.exists(pitch_model_path):
                self.pitch_model = YOLO(pitch_model_path)
                self.log_message("Pitch detection model loaded successfully")
            else:
                # Use default YOLO model for pitch detection
                self.pitch_model = YOLO('yolov8n.pt')
                self.log_message("Using default YOLO model for pitch detection")
            
            # Initialize sports library components
            try:
                self.team_classifier = TeamClassifier(device=self.device_var.get())
                self.ball_tracker = BallTracker(buffer_size=20)
                self.ball_annotator = BallAnnotator(radius=6, buffer_size=10)
                self.tracker = sv.ByteTrack(minimum_consecutive_frames=3)
                self.log_message("Sports library components initialized successfully")
            except Exception as sports_error:
                self.log_message(f"Sports library initialization error: {sports_error}")
                self.log_message("Some advanced features may not be available")
            
            self.log_message("All detection models initialized successfully")
            
        except Exception as e:
            self.log_message(f"Error initializing models: {str(e)}")
            self.log_message("Live analysis will use simulated detections")
            
        # Check what sports components are available
        self.check_sports_availability()
        
    def check_sports_availability(self):
        """Check which sports library components are available"""
        available_components = []
        
        if hasattr(self, 'team_classifier') and self.team_classifier:
            available_components.append("Team Classification")
        if hasattr(self, 'ball_tracker') and self.ball_tracker:
            available_components.append("Ball Tracking")
        if hasattr(self, 'ball_annotator') and self.ball_annotator:
            available_components.append("Ball Annotation")
        if hasattr(self, 'tracker') and self.tracker:
            available_components.append("Player Tracking")
            
        if available_components:
            self.log_message(f"Available sports components: {', '.join(available_components)}")
        else:
            self.log_message("No sports library components available - using basic detection only")
            
        return available_components
        
    def on_window_resize(self, event):
        """Handle window resize events"""
        # Only handle main window resize, not child widget resizes
        if event.widget == self.root:
            # Update canvas scroll regions if needed
            self.root.after(100, self.update_scroll_regions)
            
    def update_scroll_regions(self):
        """Update scroll regions for all canvases"""
        try:
            # This will be called after window resize to ensure proper scrolling
            pass
        except:
            pass
        
    def setup_config_tab(self):
        """Setup the configuration tab with all settings"""
        # Create a frame to hold both scrollbars and canvas
        scroll_frame = tk.Frame(self.config_tab)
        scroll_frame.pack(fill="both", expand=True)
        
        # Create a canvas with scrollbars for the configuration tab
        canvas = tk.Canvas(scroll_frame, bg='#f0f0f0', highlightthickness=0)
        v_scrollbar = ttk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
        h_scrollbar = ttk.Scrollbar(scroll_frame, orient="horizontal", command=canvas.xview)
        scrollable_frame = tk.Frame(canvas, bg='#f0f0f0')
        
        # Store canvas reference for later use
        self.config_canvas = canvas
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack the scrollbars and canvas
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")
        canvas.pack(side="left", fill="both", expand=True)
        
        # Main content frame
        main_frame = tk.Frame(scrollable_frame, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Video selection section
        self.create_video_section(main_frame)
        
        # Analysis configuration section
        self.create_config_section(main_frame)
        
        # Output section
        self.create_output_section(main_frame)
        
        # Control buttons
        self.create_control_buttons(main_frame)
        
        # Progress and log section
        self.create_progress_section(main_frame)
        
        # Bind mouse wheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # Bind arrow keys for scrolling
        def _on_key_press(event):
            if event.keysym == 'Up':
                canvas.yview_scroll(-1, "units")
            elif event.keysym == 'Down':
                canvas.yview_scroll(1, "units")
            elif event.keysym == 'Left':
                canvas.xview_scroll(-1, "units")
            elif event.keysym == 'Right':
                canvas.xview_scroll(1, "units")
        canvas.bind_all("<Key>", _on_key_press)
        
        # Add scroll indicator
        self.add_scroll_indicator(main_frame, "Configuration")
        
    def setup_live_tab(self):
        """Setup the live analysis tab with video display"""
        # Create a frame to hold both scrollbars and canvas
        scroll_frame = tk.Frame(self.live_tab)
        scroll_frame.pack(fill="both", expand=True)
        
        # Create a canvas with scrollbars for the live analysis tab
        canvas = tk.Canvas(scroll_frame, bg='#f0f0f0', highlightthickness=0)
        v_scrollbar = ttk.Scrollbar(scroll_frame, orient="vertical", command=canvas.yview)
        h_scrollbar = ttk.Scrollbar(scroll_frame, orient="horizontal", command=canvas.xview)
        scrollable_frame = tk.Frame(canvas, bg='#f0f0f0')
        
        # Store canvas reference for later use
        self.live_canvas = canvas
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=v_scrollbar.set, xscrollcommand=h_scrollbar.set)
        
        # Pack the scrollbars and canvas
        v_scrollbar.pack(side="right", fill="y")
        h_scrollbar.pack(side="bottom", fill="x")
        canvas.pack(side="left", fill="both", expand=True)
        
        # Main content frame
        main_frame = tk.Frame(scrollable_frame, bg='#f0f0f0')
        main_frame.pack(fill='both', expand=True, padx=20, pady=10)
        
        # Video display section
        self.create_video_display_section(main_frame)
        
        # Analysis controls for live view
        self.create_live_controls_section(main_frame)
        
        # Bind mouse wheel to canvas
        def _on_mousewheel(event):
            canvas.yview_scroll(int(-1*(event.delta/120)), "units")
        canvas.bind_all("<MouseWheel>", _on_mousewheel)
        
        # Bind arrow keys for scrolling
        def _on_key_press(event):
            if event.keysym == 'Up':
                canvas.yview_scroll(-1, "units")
            elif event.keysym == 'Down':
                canvas.yview_scroll(1, "units")
            elif event.keysym == 'Left':
                canvas.xview_scroll(-1, "units")
            elif event.keysym == 'Right':
                canvas.xview_scroll(1, "units")
        canvas.bind_all("<Key>", _on_key_press)
        
        # Add scroll indicator
        self.add_scroll_indicator(main_frame, "Live Analysis")
        
    def create_video_display_section(self, parent):
        """Create the video display area"""
        # Video display frame
        video_frame = tk.LabelFrame(
            parent, 
            text="📹 Live Video Analysis", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        video_frame.pack(fill='both', expand=True, pady=(0, 15))
        
        # Video canvas
        self.video_canvas = tk.Canvas(
            video_frame,
            bg='#2c3e50',
            width=800,
            height=450
        )
        self.video_canvas.pack(expand=True, fill='both', padx=15, pady=15)
        
        # Video info label
        self.live_video_info = tk.Label(
            video_frame,
            text="No video loaded. Select a video in the Configuration tab to start live analysis.",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.live_video_info.pack(pady=(0, 10))
        
    def create_live_controls_section(self, parent):
        """Create controls for live analysis"""
        # Controls frame
        controls_frame = tk.LabelFrame(
            parent, 
            text="🎮 Live Controls", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        controls_frame.pack(fill='x', pady=(0, 15))
        
        controls_content = tk.Frame(controls_frame, bg='#f0f0f0')
        controls_content.pack(fill='x', padx=15, pady=15)
        
        # Live analysis button
        self.live_start_button = tk.Button(
            controls_content, 
            text="🎬 Start Live Analysis", 
            command=self.start_live_analysis,
            bg='#27ae60',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=8,
            state='disabled'
        )
        self.live_start_button.pack(side='left')
        
        # Stop live analysis button
        self.live_stop_button = tk.Button(
            controls_content, 
            text="⏹️ Stop Live Analysis", 
            command=self.stop_live_analysis,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=8,
            state='disabled'
        )
        self.live_stop_button.pack(side='left', padx=(10, 0))
        
        # Confidence threshold slider
        confidence_frame = tk.Frame(controls_content, bg='#f0f0f0')
        confidence_frame.pack(side='left', padx=(20, 0))
        
        tk.Label(
            confidence_frame,
            text="Confidence:",
            font=('Arial', 10, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        ).pack(side='left')
        
        self.confidence_slider = tk.Scale(
            confidence_frame,
            from_=0.1,
            to=0.9,
            resolution=0.05,
            orient='horizontal',
            variable=tk.DoubleVar(value=self.confidence_threshold),
            command=self.update_confidence,
            bg='#f0f0f0',
            fg='#2c3e50',
            highlightthickness=0,
            length=150
        )
        self.confidence_slider.pack(side='left', padx=(10, 0))
        
        self.confidence_label = tk.Label(
            confidence_frame,
            text=f"{self.confidence_threshold:.2f}",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        self.confidence_label.pack(side='left', padx=(5, 0))
        
        # Frame info label
        self.frame_info_label = tk.Label(
            controls_content,
            text="Frame: 0 | FPS: 0 | Status: Ready",
            font=('Arial', 10),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        self.frame_info_label.pack(side='right')
        
    def create_video_section(self, parent):
        # Video selection frame
        video_frame = tk.LabelFrame(
            parent, 
            text="📹 Video Selection", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        video_frame.pack(fill='x', pady=(0, 15))
        
        # Source video
        source_frame = tk.Frame(video_frame, bg='#f0f0f0')
        source_frame.pack(fill='x', padx=15, pady=10)
        
        tk.Label(
            source_frame, 
            text="Source Video:", 
            font=('Arial', 10, 'bold'),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        source_input_frame = tk.Frame(source_frame, bg='#f0f0f0')
        source_input_frame.pack(fill='x', pady=(5, 0))
        
        tk.Entry(
            source_input_frame, 
            textvariable=self.source_video_path, 
            font=('Arial', 10),
            width=50
        ).pack(side='left', fill='x', expand=True)
        
        tk.Button(
            source_input_frame, 
            text="Browse", 
            command=self.browse_source_video,
            bg='#3498db',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=20
        ).pack(side='right', padx=(10, 0))
        
        # Video info display
        self.video_info_label = tk.Label(
            source_frame, 
            text="No video selected", 
            font=('Arial', 9),
            bg='#f0f0f0',
            fg='#7f8c8d'
        )
        self.video_info_label.pack(anchor='w', pady=(5, 0))
        
    def create_config_section(self, parent):
        # Configuration frame
        config_frame = tk.LabelFrame(
            parent, 
            text="⚙️ Analysis Configuration", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        config_frame.pack(fill='x', pady=(0, 15))
        
        config_content = tk.Frame(config_frame, bg='#f0f0f0')
        config_content.pack(fill='x', padx=15, pady=15)
        
        # Analysis mode selection
        mode_frame = tk.Frame(config_content, bg='#f0f0f0')
        mode_frame.pack(fill='x', pady=(0, 15))
        
        tk.Label(
            mode_frame, 
            text="Analysis Mode:", 
            font=('Arial', 10, 'bold'),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        mode_combo = ttk.Combobox(
            mode_frame, 
            textvariable=self.mode_var,
            values=list(self.modes.keys()),
            state='readonly',
            font=('Arial', 10),
            width=30
        )
        mode_combo.pack(anchor='w', pady=(5, 0))
        mode_combo.bind('<<ComboboxSelected>>', self.on_mode_change)
        
        # Mode description
        self.mode_desc_label = tk.Label(
            mode_frame, 
            text=self.modes[self.mode_var.get()],
            font=('Arial', 9),
            bg='#f0f0f0',
            fg='#7f8c8d',
            wraplength=400
        )
        self.mode_desc_label.pack(anchor='w', pady=(5, 0))
        
        # Device selection
        device_frame = tk.Frame(config_content, bg='#f0f0f0')
        device_frame.pack(fill='x')
        
        tk.Label(
            device_frame, 
            text="Processing Device:", 
            font=('Arial', 10, 'bold'),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        device_combo = ttk.Combobox(
            device_frame, 
            textvariable=self.device_var,
            values=['cpu', 'cuda', 'mps'],
            state='readonly',
            font=('Arial', 10),
            width=15
        )
        device_combo.pack(anchor='w', pady=(5, 0))
        
    def create_output_section(self, parent):
        # Output frame
        output_frame = tk.LabelFrame(
            parent, 
            text="📁 Output Configuration", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        output_frame.pack(fill='x', pady=(0, 15))
        
        output_content = tk.Frame(output_frame, bg='#f0f0f0')
        output_content.pack(fill='x', padx=15, pady=15)
        
        # Output directory
        output_dir_frame = tk.Frame(output_content, bg='#f0f0f0')
        output_dir_frame.pack(fill='x')
        
        tk.Label(
            output_dir_frame, 
            text="Output Directory:", 
            font=('Arial', 10, 'bold'),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        output_input_frame = tk.Frame(output_dir_frame, bg='#f0f0f0')
        output_input_frame.pack(fill='x', pady=(5, 0))
        
        tk.Entry(
            output_input_frame, 
            textvariable=self.output_dir, 
            font=('Arial', 10),
            width=50
        ).pack(side='left', fill='x', expand=True)
        
        tk.Button(
            output_input_frame, 
            text="Browse", 
            command=self.browse_output_dir,
            bg='#3498db',
            fg='white',
            font=('Arial', 10, 'bold'),
            relief='flat',
            padx=20
        ).pack(side='right', padx=(10, 0))
        
        # Auto-generate output path
        self.auto_output_var = tk.BooleanVar(value=True)
        tk.Checkbutton(
            output_content, 
            text="Auto-generate output filename", 
            variable=self.auto_output_var,
            command=self.update_output_path,
            bg='#f0f0f0',
            font=('Arial', 9)
        ).pack(anchor='w', pady=(10, 0))
        
    def create_control_buttons(self, parent):
        # Control buttons frame
        button_frame = tk.Frame(parent, bg='#f0f0f0')
        button_frame.pack(fill='x', pady=(0, 15))
        
        # Start analysis button
        self.start_button = tk.Button(
            button_frame, 
            text="🚀 Start Analysis", 
            command=self.start_analysis,
            bg='#27ae60',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=30,
            pady=10,
            state='disabled'
        )
        self.start_button.pack(side='left')
        
        # Stop analysis button
        self.stop_button = tk.Button(
            button_frame, 
            text="⏹️ Stop Analysis", 
            command=self.stop_analysis,
            bg='#e74c3c',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=30,
            pady=10,
            state='disabled'
        )
        self.stop_button.pack(side='left', padx=(10, 0))
        
        # Open output folder button
        self.open_output_button = tk.Button(
            button_frame, 
            text="📂 Open Output Folder", 
            command=self.open_output_folder,
            bg='#9b59b6',
            fg='white',
            font=('Arial', 12, 'bold'),
            relief='flat',
            padx=20,
            pady=10
        )
        self.open_output_button.pack(side='right')
        
    def create_progress_section(self, parent):
        # Progress frame
        progress_frame = tk.LabelFrame(
            parent, 
            text="📊 Progress & Logs", 
            font=('Arial', 12, 'bold'),
            bg='#f0f0f0',
            fg='#2c3e50'
        )
        progress_frame.pack(fill='both', expand=True)
        
        progress_content = tk.Frame(progress_frame, bg='#f0f0f0')
        progress_content.pack(fill='both', expand=True, padx=15, pady=15)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(
            progress_content, 
            variable=self.progress_var,
            maximum=100,
            length=400
        )
        self.progress_bar.pack(fill='x', pady=(0, 10))
        
        # Progress label
        self.progress_label = tk.Label(
            progress_content, 
            text="Ready to start analysis", 
            font=('Arial', 10),
            bg='#f0f0f0'
        )
        self.progress_label.pack(anchor='w')
        
        # Log text area
        log_frame = tk.Frame(progress_content, bg='#f0f0f0')
        log_frame.pack(fill='both', expand=True, pady=(10, 0))
        
        tk.Label(
            log_frame, 
            text="Analysis Logs:", 
            font=('Arial', 10, 'bold'),
            bg='#f0f0f0'
        ).pack(anchor='w')
        
        self.log_text = scrolledtext.ScrolledText(
            log_frame,
            height=8,
            font=('Consolas', 9),
            bg='#2c3e50',
            fg='#ecf0f1'
        )
        self.log_text.pack(fill='both', expand=True, pady=(5, 0))
        
    def browse_source_video(self):
        file_path = filedialog.askopenfilename(
            title="Select Video File",
            filetypes=[
                ("Video files", "*.mp4 *.avi *.mov *.mkv *.wmv"),
                ("All files", "*.*")
            ]
        )
        if file_path:
            self.source_video_path.set(file_path)
            self.update_video_info(file_path)
            self.update_output_path()
            self.validate_inputs()
            
    def browse_output_dir(self):
        dir_path = filedialog.askdirectory(title="Select Output Directory")
        if dir_path:
            self.output_dir.set(dir_path)
            self.update_output_path()
            
    def update_video_info(self, video_path):
        try:
            # Get file size
            file_size = os.path.getsize(video_path)
            size_mb = file_size / (1024 * 1024)
            
            # Get file name
            file_name = os.path.basename(video_path)
            
            info_text = f"File: {file_name} | Size: {size_mb:.1f} MB"
            self.video_info_label.config(text=info_text)
            
        except Exception as e:
            self.video_info_label.config(text=f"Error reading video info: {str(e)}")
            
    def update_output_path(self):
        if self.auto_output_var.get() and self.source_video_path.get():
            source_path = Path(self.source_video_path.get())
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            mode_name = self.mode_var.get().lower()
            
            if not self.output_dir.get():
                self.output_dir.set(str(source_path.parent))
                
            output_filename = f"{source_path.stem}_{mode_name}_{timestamp}.mp4"
            output_path = Path(self.output_dir.get()) / output_filename
            
            # Update the output path display
            self.output_path = str(output_path)
            
    def on_mode_change(self, event=None):
        # Update mode description
        selected_mode = self.mode_var.get()
        self.mode_desc_label.config(text=self.modes.get(selected_mode, ""))
        
        # Update output path if auto-generate is enabled
        if self.auto_output_var.get():
            self.update_output_path()
            
    def validate_inputs(self):
        # Check if source video is selected
        if self.source_video_path.get() and os.path.exists(self.source_video_path.get()):
            self.start_button.config(state='normal')
            self.live_start_button.config(state='normal')
            self.update_live_video_info()
        else:
            self.start_button.config(state='disabled')
            self.live_start_button.config(state='disabled')
            
    def log_message(self, message):
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()
        
    def start_analysis(self):
        if not self.source_video_path.get():
            messagebox.showerror("Error", "Please select a source video file.")
            return
            
        if not self.output_dir.get():
            messagebox.showerror("Error", "Please select an output directory.")
            return
            
        # Update UI state
        self.is_processing = True
        self.start_button.config(state='disabled')
        self.stop_button.config(state='normal')
        self.progress_label.config(text="Analysis in progress...")
        
        # Clear log
        self.log_text.delete(1.0, tk.END)
        
        # Start analysis in separate thread
        self.analysis_thread = threading.Thread(target=self.run_analysis)
        self.analysis_thread.daemon = True
        self.analysis_thread.start()
        
    def stop_analysis(self):
        self.is_processing = False
        self.progress_label.config(text="Analysis stopped by user")
        self.start_button.config(state='normal')
        self.stop_button.config(state='disabled')
        
    def run_analysis(self):
        try:
            source_path = self.source_video_path.get()
            output_path = self.output_path
            device = self.device_var.get()
            mode = self.mode_var.get()
            
            self.log_message(f"Starting {mode} analysis...")
            self.log_message(f"Source: {source_path}")
            self.log_message(f"Output: {output_path}")
            self.log_message(f"Device: {device}")
            
            # Run the sports analysis
            self.run_sports_analysis(source_path, output_path, device, mode)
            
            if self.is_processing:
                self.log_message("Analysis completed successfully!")
                self.progress_label.config(text="Analysis completed!")
                self.progress_var.set(100)
                
                # Show completion message
                messagebox.showinfo("Success", f"Analysis completed!\nOutput saved to: {output_path}")
                
        except Exception as e:
            error_msg = f"Analysis failed: {str(e)}"
            self.log_message(error_msg)
            self.progress_label.config(text="Analysis failed!")
            messagebox.showerror("Error", error_msg)
            
        finally:
            # Reset UI state
            self.is_processing = False
            self.start_button.config(state='normal')
            self.stop_button.config(state='disabled')
            
    def run_sports_analysis(self, source_path, output_path, device, mode):
        # Get the path to the main.py script
        script_dir = os.path.join(os.path.dirname(__file__), 'examples', 'soccer')
        main_script = os.path.join(script_dir, 'main.py')
        
        if not os.path.exists(main_script):
            raise FileNotFoundError(f"Main script not found: {main_script}")
            
        # Prepare command
        cmd = [
            sys.executable, main_script,
            '--source_video_path', source_path,
            '--target_video_path', output_path,
            '--device', device,
            '--mode', mode
        ]
        
        self.log_message(f"Running command: {' '.join(cmd)}")
        
        # Run the command
        process = subprocess.Popen(
            cmd,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,
            universal_newlines=True,
            cwd=script_dir
        )
        
        # Monitor output
        for line in process.stdout:
            if not self.is_processing:
                process.terminate()
                break
            self.log_message(line.strip())
            
        process.wait()
        
        if process.returncode != 0 and self.is_processing:
            raise subprocess.CalledProcessError(process.returncode, cmd)
            
    def open_output_folder(self):
        if self.output_dir.get() and os.path.exists(self.output_dir.get()):
            if os.name == 'nt':  # Windows
                os.startfile(self.output_dir.get())
            elif os.name == 'posix':  # macOS and Linux
                subprocess.run(['open', self.output_dir.get()])
        else:
            messagebox.showwarning("Warning", "Output directory not set or doesn't exist.")
            
    def update_live_video_info(self):
        """Update live video information"""
        if self.source_video_path.get():
            video_path = self.source_video_path.get()
            file_name = os.path.basename(video_path)
            self.live_video_info.config(text=f"Video: {file_name}")
        else:
            self.live_video_info.config(text="No video loaded. Select a video in the Configuration tab to start live analysis.")
            
    def start_live_analysis(self):
        """Start live video analysis"""
        if not self.source_video_path.get():
            messagebox.showerror("Error", "Please select a source video file first.")
            return
            
        try:
            # Open video capture
            self.cap = cv2.VideoCapture(self.source_video_path.get())
            if not self.cap.isOpened():
                messagebox.showerror("Error", "Could not open video file.")
                return
                
            # Get video properties
            fps = self.cap.get(cv2.CAP_PROP_FPS)
            frame_count = int(self.cap.get(cv2.CAP_PROP_FRAME_COUNT))
            
            if fps <= 0:
                fps = 30.0  # Default FPS if not available
            if frame_count <= 0:
                frame_count = 1000  # Default frame count if not available
                
            self.log_message(f"Video opened successfully: {fps:.1f} FPS, {frame_count} frames")
            
            self.is_live_analysis = True
            self.live_start_button.config(state='disabled')
            self.live_stop_button.config(state='normal')
            
            # Reset counters
            self.frame_count = 0
            self.fps_counter = 0
            self.last_fps_time = datetime.now()
            
            # Start live analysis thread
            self.live_analysis_thread = threading.Thread(target=self.run_live_analysis)
            self.live_analysis_thread.daemon = True
            self.live_analysis_thread.start()
            
        except Exception as e:
            messagebox.showerror("Error", f"Failed to start live analysis: {str(e)}")
            self.log_message(f"Live analysis start error: {str(e)}")
            
    def stop_live_analysis(self):
        """Stop live video analysis"""
        self.is_live_analysis = False
        if self.cap:
            self.cap.release()
            self.cap = None
            
        self.live_start_button.config(state='normal')
        self.live_stop_button.config(state='disabled')
        self.frame_info_label.config(text="Frame: 0 | FPS: 0 | Status: Stopped")
        
        # Clear canvas
        self.video_canvas.delete("all")
        self.video_canvas.create_text(
            400, 225,
            text="Live analysis stopped",
            fill="white",
            font=('Arial', 16)
        )
        
        self.log_message("Live analysis stopped")
        
    def update_confidence(self, value):
        """Update confidence threshold for detection"""
        self.confidence_threshold = float(value)
        self.confidence_label.config(text=f"{self.confidence_threshold:.2f}")
        self.log_message(f"Confidence threshold updated to {self.confidence_threshold:.2f}")
        
    def run_live_analysis(self):
        """Run live video analysis"""
        try:
            while self.is_live_analysis and self.cap and self.cap.isOpened():
                ret, frame = self.cap.read()
                if not ret:
                    # End of video, restart
                    self.cap.set(cv2.CAP_PROP_POS_FRAMES, 0)
                    continue
                    
                # Process frame with selected mode
                processed_frame = self.process_frame_live(frame)
                
                # Update display
                self.update_video_display(processed_frame)
                
                # Update frame info
                self.frame_count += 1
                self.fps_counter += 1
                
                # Calculate FPS every second
                current_time = datetime.now()
                if (current_time - self.last_fps_time).total_seconds() >= 1.0:
                    fps = self.fps_counter
                    self.fps_counter = 0
                    self.last_fps_time = current_time
                    
                    # Update frame info in main thread
                    self.root.after(0, self.update_frame_info, self.frame_count, fps)
                    
                # Control frame rate using time.sleep instead of cv2.waitKey
                import time
                time.sleep(0.033)  # ~30 FPS (1/30 seconds)
                
        except Exception as e:
            error_msg = f"Live analysis error: {str(e)}"
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
            self.root.after(0, lambda: self.log_message(error_msg))
            self.root.after(0, self.stop_live_analysis)
            
    def process_frame_live(self, frame):
        """Process a single frame for live analysis using real sports detection modules"""
        try:
            # Get current mode and device
            mode = self.mode_var.get()
            device = self.device_var.get()
            
            processed_frame = frame.copy()
            h, w = frame.shape[:2]
            
            # Use real sports detection modules if available
            if SPORTS_AVAILABLE and self.player_model and self.ball_model and self.pitch_model:
                try:
                    if mode == "PLAYER_DETECTION":
                        # Real player detection using YOLO + sports annotation
                        result = self.player_model(frame, imgsz=640, verbose=False, conf=self.confidence_threshold)[0]
                        detections = sv.Detections.from_ultralytics(result)
                        
                        # Filter for players (class 0, 1, 2, 3: ball, goalkeeper, player, referee)
                        player_detections = detections[detections.class_id.isin([1, 2, 3])]
                        
                        if len(player_detections) > 0:
                            # Use sports BoxAnnotator with proper colors
                            processed_frame = sv.BoxAnnotator(
                                color=sv.ColorPalette.from_hex(['#00FF00', '#00FFFF', '#FF00FF']),
                                thickness=2
                            ).annotate(processed_frame, player_detections)
                            
                            # Add labels using sports LabelAnnotator
                            labels = []
                            for class_id in player_detections.class_id:
                                if class_id == 1:
                                    labels.append("Goalkeeper")
                                elif class_id == 2:
                                    labels.append("Player")
                                elif class_id == 3:
                                    labels.append("Referee")
                                else:
                                    labels.append("Unknown")
                            
                            processed_frame = sv.LabelAnnotator(
                                color=sv.ColorPalette.from_hex(['#00FF00', '#00FFFF', '#FF00FF']),
                                text_color=sv.Color.WHITE,
                                text_thickness=1
                            ).annotate(processed_frame, player_detections, labels=labels)
                        else:
                            # Fallback to simulated detection if no players found
                            self.draw_simulated_players(processed_frame, h, w)
                            
                    elif mode == "BALL_DETECTION":
                        # Real ball detection using sports BallAnnotator and BallTracker
                        result = self.ball_model(frame, imgsz=640, verbose=False, conf=self.confidence_threshold)[0]
                        detections = sv.Detections.from_ultralytics(result)
                        
                        # Filter for ball (class 0)
                        ball_detections = detections[detections.class_id == 0]
                        
                        if len(ball_detections) > 0:
                            # Use sports BallTracker and BallAnnotator
                            tracked_detections = self.ball_tracker.update(ball_detections)
                            processed_frame = self.ball_annotator.annotate(processed_frame, tracked_detections)
                        else:
                            # Fallback to simulated ball detection
                            self.draw_simulated_ball(processed_frame, h, w)
                            
                    elif mode == "PITCH_DETECTION":
                        # Real pitch detection using sports soccer pitch drawing
                        result = self.pitch_model(frame, imgsz=640, verbose=False, conf=self.confidence_threshold)[0]
                        detections = sv.Detections.from_ultralytics(result)
                        
                        if len(detections) > 0:
                            # Use sports soccer pitch drawing functions
                            try:
                                # Create soccer pitch configuration
                                pitch_config = SoccerPitchConfiguration()
                                
                                # Draw pitch keypoints using sports functions
                                processed_frame = sv.PointAnnotator(
                                    color=sv.Color.RED,
                                    radius=8
                                ).annotate(processed_frame, detections)
                                
                                # Draw pitch lines using sports soccer functions
                                if len(detections) >= 4:
                                    # Get pitch keypoints and draw them on pitch
                                    keypoints = detections.get_anchors_coordinates(sv.Position.CENTER)
                                    
                                    # Create a pitch overlay using sports functions
                                    pitch_overlay = draw_points_on_pitch(
                                        config=pitch_config,
                                        xy=keypoints,
                                        face_color=sv.Color.YELLOW,
                                        edge_color=sv.Color.WHITE,
                                        radius=6,
                                        thickness=2,
                                        padding=50,
                                        scale=0.05  # Scale for video frame
                                    )
                                    
                                    # Blend pitch overlay with frame
                                    if pitch_overlay.shape[:2] == (h, w):
                                        # Resize pitch overlay to match frame
                                        pitch_overlay_resized = cv2.resize(pitch_overlay, (w, h))
                                        # Blend with alpha
                                        alpha = 0.3
                                        processed_frame = cv2.addWeighted(processed_frame, 1-alpha, pitch_overlay_resized, alpha, 0)
                                    else:
                                        # Simple pitch outline if overlay doesn't match
                                        cv2.rectangle(processed_frame, (50, 50), (w-50, h-50), (0, 255, 255), 3)
                                        cv2.line(processed_frame, (w//2, 50), (w//2, h-50), (0, 255, 255), 3)
                                        cv2.line(processed_frame, (50, h//2), (w-50, h//2), (0, 255, 255), 3)
                            except Exception as pitch_error:
                                self.log_message(f"Pitch drawing error: {pitch_error}")
                                # Fallback to simple pitch outline
                                cv2.rectangle(processed_frame, (50, 50), (w-50, h-50), (0, 255, 255), 3)
                                cv2.line(processed_frame, (w//2, 50), (w//2, h-50), (0, 255, 255), 3)
                                cv2.line(processed_frame, (50, h//2), (w-50, h//2), (0, 255, 255), 3)
                        else:
                            # Fallback to simulated pitch detection
                            self.draw_simulated_pitch(processed_frame, h, w)
                            
                    elif mode == "PLAYER_TRACKING":
                        # Real player tracking using sports tracking
                        result = self.player_model(frame, imgsz=640, verbose=False, conf=self.confidence_threshold)[0]
                        detections = sv.Detections.from_ultralytics(result)
                        
                        # Update tracker using sports ByteTrack
                        tracked_detections = self.tracker.update_with_detections(detections)
                        
                        if len(tracked_detections) > 0:
                            # Use sports EllipseAnnotator for tracking
                            processed_frame = sv.EllipseAnnotator(
                                color=sv.ColorPalette.from_hex(['#FF1493', '#00BFFF', '#FF6347', '#FFD700']),
                                thickness=2
                            ).annotate(processed_frame, tracked_detections)
                            
                            # Add tracking IDs using sports LabelAnnotator
                            labels = [f"ID: {tracker_id}" for tracker_id in tracked_detections.tracker_id]
                            processed_frame = sv.LabelAnnotator(
                                color=sv.ColorPalette.from_hex(['#FF1493', '#00BFFF', '#FF6347', '#FFD700']),
                                text_color=sv.Color.WHITE,
                                text_thickness=1
                            ).annotate(processed_frame, tracked_detections, labels=labels)
                        else:
                            # Fallback to simulated tracking
                            self.draw_simulated_tracking(processed_frame, h, w)
                            
                    elif mode == "TEAM_CLASSIFICATION":
                        # Real team classification using sports TeamClassifier
                        result = self.player_model(frame, imgsz=640, verbose=False, conf=self.confidence_threshold)[0]
                        detections = sv.Detections.from_ultralytics(result)
                        
                        # Filter for players only
                        player_detections = detections[detections.class_id.isin([1, 2])]
                        
                        if len(player_detections) > 0:
                            # Extract player crops for team classification
                            crops = [sv.crop_image(frame, xyxy) for xyxy in player_detections.xyxy]
                            
                            try:
                                # Use sports TeamClassifier to predict team labels
                                team_labels = self.team_classifier.predict(crops)
                                
                                # Annotate with team colors using sports annotators
                                colors = ['#FF0000' if label == 0 else '#0000FF' for label in team_labels]
                                processed_frame = sv.BoxAnnotator(
                                    color=sv.ColorPalette.from_hex(colors),
                                    thickness=2
                                ).annotate(processed_frame, player_detections)
                                
                                # Add team labels using sports LabelAnnotator
                                labels = [f"Team {'A' if label == 0 else 'B'}" for label in team_labels]
                                processed_frame = sv.LabelAnnotator(
                                    color=sv.ColorPalette.from_hex(colors),
                                    text_color=sv.Color.WHITE,
                                    text_thickness=1
                                ).annotate(processed_frame, player_detections, labels=labels)
                            except Exception as team_error:
                                self.log_message(f"Team classification error: {team_error}")
                                # Fallback to simulated team classification
                                self.draw_simulated_teams(processed_frame, h, w)
                        else:
                            # Fallback to simulated team classification
                            self.draw_simulated_teams(processed_frame, h, w)
                            
                    elif mode == "RADAR":
                        # Real radar view using sports player positions
                        result = self.player_model(frame, imgsz=640, verbose=False, conf=self.confidence_threshold)[0]
                        detections = sv.Detections.from_ultralytics(result)
                        
                        if len(detections) > 0:
                            # Create radar view using sports functions
                            processed_frame = self.create_radar_view(frame, detections)
                        else:
                            # Fallback to simulated radar
                            self.draw_simulated_radar(processed_frame, h, w)
                            
                except Exception as e:
                    self.log_message(f"Real detection error: {str(e)}")
                    # Fallback to simulated detection
                    self.fallback_to_simulation(processed_frame, mode, h, w)
            else:
                # Use simulated detection if models not available
                self.fallback_to_simulation(processed_frame, mode, h, w)
            
            # Add mode label and timestamp
            cv2.putText(processed_frame, f"Mode: {mode}", (10, h - 40), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            timestamp = datetime.now().strftime("%H:%M:%S")
            cv2.putText(processed_frame, f"Time: {timestamp}", (10, h - 20), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (255, 255, 255), 2)
            
            return processed_frame
            
        except Exception as e:
            self.log_message(f"Frame processing error: {e}")
            return frame
            
    def fallback_to_simulation(self, frame, mode, h, w):
        """Fallback to simulated detection when real models fail"""
        if mode == "PLAYER_DETECTION":
            self.draw_simulated_players(frame, h, w)
        elif mode == "BALL_DETECTION":
            self.draw_simulated_ball(frame, h, w)
        elif mode == "PITCH_DETECTION":
            self.draw_simulated_pitch(frame, h, w)
        elif mode == "PLAYER_TRACKING":
            self.draw_simulated_tracking(frame, h, w)
        elif mode == "TEAM_CLASSIFICATION":
            self.draw_simulated_teams(frame, h, w)
        elif mode == "RADAR":
            self.draw_simulated_radar(frame, h, w)
            
    def draw_simulated_players(self, frame, h, w):
        """Draw simulated player detections"""
        cv2.rectangle(frame, (w//4, h//4), (w//4 + 100, h//4 + 200), (0, 255, 0), 2)
        cv2.putText(frame, "Player", (w//4, h//4 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 0), 2)
        cv2.rectangle(frame, (3*w//4, h//3), (3*w//4 + 80, h//3 + 160), (0, 255, 0), 2)
        cv2.putText(frame, "Player", (3*w//4, h//3 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.6, (0, 255, 0), 2)
        
    def draw_simulated_ball(self, frame, h, w):
        """Draw simulated ball detection"""
        cv2.circle(frame, (w//2, h//2), 20, (255, 0, 0), -1)
        cv2.putText(frame, "Ball", (w//2 - 20, h//2 + 40), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 0), 2)
        for i in range(1, 5):
            cv2.circle(frame, (w//2 - i*10, h//2 - i*5), 15-i*2, (255, 0, 0), 1)
            
    def draw_simulated_pitch(self, frame, h, w):
        """Draw simulated pitch detection"""
        cv2.line(frame, (0, h//2), (w, h//2), (0, 255, 255), 3)
        cv2.line(frame, (w//2, 0), (w//2, h), (0, 255, 255), 3)
        cv2.ellipse(frame, (0, 0), (50, 50), 0, 0, 90, (0, 255, 255), 2)
        cv2.ellipse(frame, (w, 0), (50, 50), 0, 90, 180, (0, 255, 255), 2)
        cv2.ellipse(frame, (0, h), (50, 50), 0, 270, 360, (0, 255, 255), 2)
        cv2.ellipse(frame, (w, h), (50, 50), 0, 180, 270, (0, 255, 255), 2)
        cv2.putText(frame, "Pitch Lines", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
    def draw_simulated_tracking(self, frame, h, w):
        """Draw simulated player tracking"""
        cv2.rectangle(frame, (w//4, h//4), (w//4 + 100, h//4 + 200), (255, 0, 255), 2)
        cv2.putText(frame, "ID: 001", (w//4, h//4 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
        cv2.rectangle(frame, (3*w//4, h//3), (3*w//4 + 80, h//3 + 160), (255, 0, 255), 2)
        cv2.putText(frame, "ID: 002", (3*w//4, h//3 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 0, 255), 2)
        
    def draw_simulated_teams(self, frame, h, w):
        """Draw simulated team classification"""
        cv2.rectangle(frame, (w//4, h//4), (w//4 + 100, h//4 + 200), (0, 0, 255), 2)
        cv2.putText(frame, "Team A", (w//4, h//4 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 0, 255), 2)
        cv2.rectangle(frame, (3*w//4, h//3), (3*w//4 + 80, h//3 + 160), (255, 255, 0), 2)
        cv2.putText(frame, "Team B", (3*w//4, h//3 - 10), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (255, 255, 0), 2)
        
    def draw_simulated_radar(self, frame, h, w):
        """Draw simulated radar view"""
        cv2.circle(frame, (w//2, h//2), 100, (0, 255, 255), 2)
        cv2.circle(frame, (w//2, h//2), 50, (0, 255, 255), 1)
        cv2.circle(frame, (w//2 - 30, h//2 - 40), 8, (0, 255, 0), -1)
        cv2.circle(frame, (w//2 + 60, h//2 + 20), 8, (255, 0, 0), -1)
        cv2.circle(frame, (w//2 - 20, h//2 + 70), 8, (0, 0, 255), -1)
        cv2.putText(frame, "Radar View", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        
    def create_radar_view(self, frame, detections):
        """Create radar view from real player detections"""
        h, w = frame.shape[:2]
        radar_frame = frame.copy()
        
        # Draw radar circles
        cv2.circle(radar_frame, (w//2, h//2), 100, (0, 255, 255), 2)
        cv2.circle(radar_frame, (w//2, h//2), 50, (0, 255, 255), 1)
        
        # Get player positions and map to radar
        if len(detections) > 0:
            centers = detections.get_anchors_coordinates(sv.Position.CENTER)
            for i, center in enumerate(centers):
                # Map position to radar scale
                radar_x = int(w//2 + (center[0] - w//2) * 0.3)
                radar_y = int(h//2 + (center[1] - h//2) * 0.3)
                
                # Draw player on radar
                color = (0, 255, 0) if i % 2 == 0 else (255, 0, 0)
                cv2.circle(radar_frame, (radar_x, radar_y), 8, color, -1)
                cv2.putText(radar_frame, f"P{i+1}", (radar_x-10, radar_y+20), 
                           cv2.FONT_HERSHEY_SIMPLEX, 0.5, color, 1)
        
        cv2.putText(radar_frame, "Radar View", (10, 30), cv2.FONT_HERSHEY_SIMPLEX, 0.7, (0, 255, 255), 2)
        return radar_frame
            
    def update_video_display(self, frame):
        """Update the video display canvas"""
        try:
            # Convert frame to PIL Image
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            pil_image = Image.fromarray(frame_rgb)
            
            # Resize to fit canvas
            canvas_width = self.video_canvas.winfo_width()
            canvas_height = self.video_canvas.winfo_height()
            
            if canvas_width > 1 and canvas_height > 1:  # Canvas is properly sized
                pil_image.thumbnail((canvas_width, canvas_height), Image.Resampling.LANCZOS)
                
            # Convert to PhotoImage
            photo = ImageTk.PhotoImage(pil_image)
            
            # Update canvas
            self.video_canvas.delete("all")
            self.video_canvas.create_image(
                canvas_width // 2, canvas_height // 2,
                image=photo, anchor="center"
            )
            
            # Keep reference to prevent garbage collection
            self.current_frame = photo
            
        except Exception as e:
            print(f"Display update error: {e}")
            
    def update_frame_info(self, frame_num, fps):
        """Update frame information display"""
        self.frame_info_label.config(text=f"Frame: {frame_num} | FPS: {fps} | Status: Running")

    def add_scroll_indicator(self, parent, tab_name):
        """Add a scroll indicator to show users they can scroll"""
        indicator_frame = tk.Frame(parent, bg='#ecf0f1', relief='raised', bd=1)
        indicator_frame.pack(fill='x', pady=(5, 0))
        
        indicator_label = tk.Label(
            indicator_frame,
            text=f"📜 Use scrollbars, mouse wheel, or arrow keys to navigate {tab_name} tab",
            font=('Arial', 9),
            bg='#ecf0f1',
            fg='#7f8c8d'
        )
        indicator_label.pack(pady=5)

def main():
    root = tk.Tk()
    app = SportsAnalyticsUI(root)
    
    # Set default output directory to current working directory
    app.output_dir.set(os.getcwd())
    
    # Center the window
    root.update_idletasks()
    x = (root.winfo_screenwidth() // 2) - (root.winfo_width() // 2)
    y = (root.winfo_screenheight() // 2) - (root.winfo_height() // 2)
    root.geometry(f"+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
